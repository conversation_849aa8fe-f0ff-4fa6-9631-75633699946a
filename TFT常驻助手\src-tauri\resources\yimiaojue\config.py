# -*- coding: utf-8 -*-
"""
配置模块 v3.0

本文件集中管理应用的所有静态配置、路径和常量。
包括截图区域坐标、识别阈值、数据库路径等。
修改任何配置参数，都应在此文件中进行。

[v3.0 更新]
- 优化了目录打包(onedir)的路径处理
- 增强了Tesseract路径的自动检测和容错机制
- 改进了中文路径的兼容性处理
"""
import os
import sys
import configparser
import logging

# --- 路径定义 (分为只读资源和可写数据) ---

def _get_app_path():
    """获取应用程序的可写数据目录"""
    if getattr(sys, 'frozen', False):
        return os.path.dirname(sys.executable)
    else:
        return os.path.dirname(os.path.abspath(__file__))

def _get_base_path():
    """获取应用程序的只读资源目录"""
    if getattr(sys, 'frozen', False):
        return sys._MEIPASS
    else:
        return os.path.dirname(os.path.abspath(__file__))

# 1. 应用可执行文件所在的目录 (用于存放日志、可写配置文件等)
APP_PATH = _get_app_path()

# 2. 应用内部资源文件所在的目录 (只读)
BASE_PATH = _get_base_path()

# [修改] 禁用Python字节码生成，避免在源码目录生成__pycache__
sys.dont_write_bytecode = True

# [诊断输出] 在调试模式下输出路径信息
if os.environ.get('YIMIAOJUE_DEBUG'):
    # 这些日志现在将由在主程序入口点配置好的根记录器处理
    logging.info(f"--- [配置诊断] ---")
    logging.info(f"APP_PATH (可写目录): {APP_PATH}")
    logging.info(f"BASE_PATH (资源目录): {BASE_PATH}")
    logging.info(f"是否为打包环境: {getattr(sys, 'frozen', False)}")
    logging.info(f"--- [配置诊断结束] ---")

# --- [新增] 动态配置读取 ---
CONFIG_INI_PATH = os.path.join(APP_PATH, 'config.ini')

# 检查文件是否存在，如果不存在，则创建默认配置
if not os.path.exists(CONFIG_INI_PATH):
    default_config = configparser.ConfigParser()
    default_config['Settings'] = {
        'debug_mode': 'False',
        'log_level_console': 'INFO',
        'log_level_file': 'INFO',
        'log_level_ocr': 'DEBUG'
    }
    try:
        with open(CONFIG_INI_PATH, 'w', encoding='utf-8') as f:
            default_config.write(f)
    except Exception as e:
        logging.warning(f"[配置警告] 无法创建配置文件: {e}")

# 读取配置
config_parser = configparser.ConfigParser()
try:
    config_parser.read(CONFIG_INI_PATH, encoding='utf-8')
    # 使用 .getboolean() 来安全地读取布尔值
    DEBUG_MODE = config_parser.getboolean('Settings', 'debug_mode', fallback=False)
    # [新增] 读取日志级别配置
    LOG_LEVEL_CONSOLE = config_parser.get('Settings', 'log_level_console', fallback='INFO').upper()
    LOG_LEVEL_FILE = config_parser.get('Settings', 'log_level_file', fallback='INFO').upper()
    LOG_LEVEL_OCR = config_parser.get('Settings', 'log_level_ocr', fallback='DEBUG').upper()
except Exception as e:
    logging.warning(f"[配置警告] 读取配置文件失败: {e}")
    DEBUG_MODE = False
    # 设置备用日志级别
    LOG_LEVEL_CONSOLE = 'INFO'
    LOG_LEVEL_FILE = 'INFO'
    LOG_LEVEL_OCR = 'DEBUG'

# --- 统一路径配置 ---
# [v3.0 重构] 使用智能路径检测函数

# 其他资源路径
# [修改] 使用根目录的共享数据库
def _get_shared_db_path():
    """获取共享数据库路径"""
    # 检查是否在临时目录运行（通过检查路径中是否包含temp）
    current_dir = os.path.dirname(os.path.abspath(__file__))

    if 'temp' in current_dir.lower() or 'tmp' in current_dir.lower():
        # 在临时目录运行，动态查找共享数据库
        logging.info(f"[配置] 检测到临时目录运行模式，当前目录: {current_dir}")

        # 策略1: 通过环境变量获取数据库路径（Tauri可以设置）
        if 'TFT_DB_PATH' in os.environ:
            env_db_path = os.environ['TFT_DB_PATH']
            if os.path.exists(env_db_path):
                logging.info(f"[配置] 使用环境变量指定的数据库: {env_db_path}")
                return env_db_path

        # 策略2: 查找常见的应用安装位置
        possible_paths = [
            # 开发环境路径
            r"C:\Users\<USER>\Desktop\yundzhenghe\tft_data.db",
            os.path.join(os.path.expanduser("~"), "Desktop", "yundzhenghe", "tft_data.db"),

            # 打包后可能的路径
            os.path.join(os.path.expanduser("~"), "AppData", "Local", "TFT常驻助手", "tft_data.db"),
            os.path.join(os.path.expanduser("~"), "Documents", "TFT常驻助手", "tft_data.db"),

            # 程序安装目录（通过注册表或其他方式获取）
            # 这里可以添加更多可能的路径
        ]

        for db_path in possible_paths:
            if os.path.exists(db_path):
                logging.info(f"[配置] 临时目录模式，找到共享数据库: {db_path}")
                return db_path

        # 策略3: 如果都找不到，尝试在当前用户目录创建
        user_db_dir = os.path.join(os.path.expanduser("~"), "AppData", "Local", "TFT常驻助手")
        os.makedirs(user_db_dir, exist_ok=True)
        user_db_path = os.path.join(user_db_dir, "tft_data.db")

        logging.warning(f"[配置] 临时目录模式，未找到共享数据库，将使用用户目录: {user_db_path}")
        return user_db_path
    else:
        # 正常模式，从 resources/yimiaojue 向上4级到根目录
        root_dir = os.path.join(current_dir, '..', '..', '..', '..')
        shared_db = os.path.join(root_dir, 'tft_data.db')
        shared_db = os.path.normpath(shared_db)

        if os.path.exists(shared_db):
            logging.info(f"[配置] 使用共享数据库: {shared_db}")
            return shared_db
        else:
            # 备用：使用本地数据库
            local_db = os.path.join(BASE_PATH, 'modules', 'tft_data.db')
            logging.warning(f"[配置] 共享数据库未找到，使用本地数据库: {local_db}")
            return local_db

HEX_DB_PATH = _get_shared_db_path()
EQUIP_DB_PATH = HEX_DB_PATH  # 使用同一个数据库
TEMP_SCREENSHOT_PATH = os.path.join(APP_PATH, 'screenshot.png')                   # [修改] 使用可写目录

# --- [新增] 统一监控与触发器配置 ---
HASH_BASES_PATH = os.path.join(BASE_PATH, 'modules', 'hash_bases.json')

# 定义触发逻辑所需的哈希匹配数量
HEX_HASH_REQUIRED_MATCH = 3
EQUIP_HASH_REQUIRED_MATCH = 3
HASH_DIFFERENCE_THRESHOLD = 4 # 感知哈希差异阈值

# 定义所有需要监控的区域
MONITOR_REGIONS = {
    "blue_button": {
        "coords": (1163/2560, 1301/1440, 1398/2560, 1347/1440),
        "label": "蓝色按钮"
    },
    "refresh_button": {
        "coords": [
            (669/2560, 1118/1440, 805/2560, 1185/1440),
            (1212/2560, 1118/1440, 1348/2560, 1185/1440),
            (1754/2560, 1118/1440, 1890/2560, 1185/1440)
        ],
        "label": "刷新按钮",
        "trigger_rule": "2_of_3"
    },
    "equipment_frame": {
        "label": "装备(仅边框)",
        "coords_outer": (349/2560, 1160/1440, 1987/2560, 1439/1440),
        "coords_inner": (355/2560, 1166/1440, 1980/2560, 1430/1440),
        "mode": "frame_hash"
    }
}

# [新增] 用于装备触发的特定颜色检查区域 (来自用户反馈)
# 原始绝对坐标: (1012, 1150, 1327, 1173)，基于2560x1440分辨率
EQUIP_COLOR_CHECK_REGION_RELATIVE = (1012/2560, 1150/1440, 1327/2560, 1173/1440)
 
 # --- OCR 相关配置 ---
 # RapidOCR 无需特殊配置
 
 # --- 海克斯截图区域配置 (基于2560x1440分辨率的相对坐标) ---
# 方案一 (常规对局)
SCREENSHOT_REGIONS_RELATIVE_PLAN1 = [
    (590/2560, 722/1440, 900/2560, 760/1440),
    (1140/2560, 722/1440, 1450/2560, 760/1440),
    (1690/2560, 722/1440, 1996/2560, 760/1440)
]
# 方案二 (刷新增益)
SCREENSHOT_REGIONS_RELATIVE_PLAN2 = [
    (510/2560, 709/1440, 815/2560, 748/1440),
    (1185/2560, 722/1440, 1490/2560, 760/1440),
    (1745/2560, 722/1440, 2050/2560, 760/1440)
]
# 默认使用方案一, 此变量会在运行时被修改
SCREENSHOT_REGIONS_RELATIVE = SCREENSHOT_REGIONS_RELATIVE_PLAN1

# --- 刷新按钮检测配置 ---
REFRESH_BUTTON_REGION_RELATIVE = (1556/2560, 1116/1440, 1696/2560, 1187/1440) # 检测区域
REFRESH_BUTTON_COLORS = [ # 颜色特征 (已弃用, 保留备查)
    (57, 42, 24),
    (56, 40, 23),
    (58, 43, 23),
    (58, 43, 25),
    (55, 39, 22)
]
COLOR_MATCH_THRESHOLD = 3 # 颜色匹配阈值 (已弃用)
REFRESH_BUTTON_SIZE = (141, 72) # 用于标准化的尺寸
REFRESH_BUTTON_PHASH = "9ac965b6a6c99964" # 感知哈希值
REFRESH_BUTTON_DHASH = "aa5533292d39510b" # 差异哈希值
REFRESH_BUTTON_HASH_THRESHOLD = 8  # 哈希匹配阈值

# --- 轮次检测配置 ---
ROUND_REGION_RELATIVE = [
    (1025/2560, 10/1440, 1085/2560, 45/1440),  # 原始区域
    (1096/2560, 10/1440, 1155/2560, 45/1440)   # 新增区域
]
TARGET_ROUNDS = ["1-4", "3-1", "4-1"]  # 目标轮次
ROUND_HASH_THRESHOLD = 3  # 轮次图像哈希阈值

# --- 装备相关 ---

# [新] 用于轮廓分析、计算装备数量的大范围截图区域
# 这个坐标来自于用户提供的截图 (374, 1183, 1966, 1420) 并转换为相对坐标
EQUIP_CONTOUR_REGION_RELATIVE = (374/2560, 1183/1440, 1966/2560, 1420/1440)

# [新] OpenCV轮廓分析的最小面积阈值，用于过滤掉噪点
MIN_EQUIP_CONTOUR_AREA = 2000 

# [新] 根据装备数量选择不同的、精准的OCR识别区域 (每个区域都是一个小长条)
# 这些坐标是根据EquipManager中的评级窗口位置精确计算得出的
# 宽度 = 评级窗口宽度 * 2.5
# 高度 = 固定的文字区域高度
# 位置 = 与对应评级窗口水平居中
# [修正] 根据用户的精准反馈，更新所有坐标以确保OCR区域的准确性
EQUIP_OCR_REGIONS_BY_COUNT = {
    # 坐标格式: (x1, y1, x2, y2)
    # 垂直高度统一为 (1328/1440, 1355/1440)
    # 水平宽度在原基础上左右各向内缩进10像素
    1: [
        (1056/2560, 1328/1440, 1280/2560, 1355/1440),
    ],
    2: [
        (899/2560, 1328/1440, 1119/2560, 1355/1440),
        (1216/2560, 1328/1440, 1436/2560, 1355/1440),
    ],
    3: [
        (741/2560, 1328/1440, 961/2560, 1355/1440),
        (1056/2560, 1328/1440, 1280/2560, 1355/1440),
        (1375/2560, 1328/1440, 1595/2560, 1355/1440),
    ],
    4: [
        (583/2560, 1328/1440, 803/2560, 1355/1440),
        (899/2560, 1328/1440, 1119/2560, 1355/1440),
        (1216/2560, 1328/1440, 1436/2560, 1355/1440),
        (1533/2560, 1328/1440, 1753/2560, 1355/1440),
    ],
    5: [
        (424/2560, 1328/1440, 644/2560, 1355/1440),
        (741/2560, 1328/1440, 961/2560, 1355/1440),
        (1056/2560, 1328/1440, 1280/2560, 1355/1440),
        (1375/2560, 1328/1440, 1595/2560, 1355/1440),
        (1692/2560, 1328/1440, 1912/2560, 1355/1440),
    ],
}

# [旧] 用于显示装备评级窗口的坐标，现在将作为新识别流程的UI布局基准
EQUIP_RATING_REGIONS_BY_COUNT = {
    1: [(1120/2560, 1293/1440, 1216/2560, 1329/1440)],
    2: [(961/2560, 1293/1440, 1057/2560, 1329/1440),
        (1278/2560, 1293/1440, 1374/2560, 1329/1440)],
    3: [(803/2560, 1293/1440, 899/2560, 1329/1440),
        (1120/2560, 1293/1440, 1216/2560, 1329/1440),
        (1437/2560, 1293/1440, 1533/2560, 1329/1440)],
    4: [(645/2560, 1293/1440, 741/2560, 1329/1440),
        (961/2560, 1293/1440, 1057/2560, 1329/1440),
        (1278/2560, 1293/1440, 1374/2560, 1329/1440),
        (1595/2560, 1293/1440, 1691/2560, 1329/1440)],
    5: [(486/2560, 1293/1440, 582/2560, 1329/1440),
        (803/2560, 1293/1440, 899/2560, 1329/1440),
        (1120/2560, 1293/1440, 1216/2560, 1329/1440),
        (1437/2560, 1293/1440, 1533/2560, 1329/1440),
        (1754/2560, 1293/1440, 1850/2560, 1329/1440)]
}

# [旧的、废弃的] 装备截图区域，现已被新的两步识别法取代
# EQUIP_SCREENSHOT_REGION_RELATIVE = (510/2560, 1320/1440, 1820/2560, 1360/1440)
# EQUIP_REGIONS_RELATIVE已经被移到下面并重命名为 EQUIP_RATING_REGIONS_BY_COUNT 