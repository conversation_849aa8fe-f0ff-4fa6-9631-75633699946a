use std::process::{Command, Stdio};
use std::path::PathBuf;
use serde::{Deserialize, Serialize};
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager};
use reqwest;
use std::fs;

#[derive(Debug, Serialize, Deserialize)]
pub struct PythonResult {
    pub success: bool,
    pub data: Option<String>,
    pub error: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct YimiaoJueStatus {
    pub running: bool,
    pub message: String,
}

/// 获取yimiaojue项目的路径
fn get_yimiaojue_path(app_handle: &AppHandle) -> Result<PathBuf, String> {
    println!("🔍 开始查找yimiaojue路径...");

    // 获取当前工作目录
    let current_dir = std::env::current_dir().unwrap_or_default();
    println!("📁 当前工作目录: {:?}", current_dir);

    // 统一使用resources目录作为唯一路径
    let possible_paths = vec![
        // 开发环境：resources目录
        PathBuf::from("resources/yimiaojue"),
        PathBuf::from("src-tauri/resources/yimiaojue"),
    ];

    // 逐个尝试路径
    for (index, path) in possible_paths.iter().enumerate() {
        println!("尝试路径 {}: {:?}", index + 1, path);

        if path.exists() {
            let absolute_path = std::fs::canonicalize(path)
                .unwrap_or_else(|_| path.clone());
            println!("✅ 找到yimiaojue路径: {:?} -> {:?}", path, absolute_path);

            // 验证是否包含ocr查询.py文件
            let main_script = path.join("ocr查询.py");
            if main_script.exists() {
                println!("✅ 确认找到ocr查询.py: {:?}", main_script);
                return Ok(path.clone());
            } else {
                println!("❌ 路径存在但未找到ocr查询.py: {:?}", main_script);
            }
        } else {
            println!("❌ 路径不存在: {:?}", path);
        }
    }

    // 尝试生产环境：应用exe同目录下的yimiaojue
    if let Ok(exe_path) = std::env::current_exe() {
        if let Some(exe_dir) = exe_path.parent() {
            let prod_yimiaojue = exe_dir.join("yimiaojue");
            if prod_yimiaojue.exists() {
                println!("找到生产环境yimiaojue路径: {:?}", prod_yimiaojue);
                return Ok(prod_yimiaojue);
            }
        }
    }

    // 尝试使用Tauri的资源目录（生产模式）
    match app_handle.path().resource_dir() {
        Ok(resource_dir) => {
            let yimiaojue_path = resource_dir.join("yimiaojue");
            if yimiaojue_path.exists() {
                println!("找到yimiaojue路径（资源目录）: {:?}", yimiaojue_path);
                return Ok(yimiaojue_path);
            }
        }
        Err(e) => {
            println!("无法获取资源目录: {}", e);
        }
    }

    // 如果都找不到，返回错误信息
    let attempted_paths: Vec<String> = possible_paths.iter()
        .map(|p| format!("{:?}", p))
        .collect();

    Err(format!(
        "yimiaojue目录不存在。尝试的路径: {}",
        attempted_paths.join(", ")
    ))
}

/// 检查Python环境
#[tauri::command]
pub async fn check_python_environment() -> Result<PythonResult, String> {
    println!("检查Python环境...");
    
    let output = Command::new("python")
        .arg("--version")
        .output();
    
    match output {
        Ok(output) => {
            if output.status.success() {
                let version = String::from_utf8_lossy(&output.stdout);
                Ok(PythonResult {
                    success: true,
                    data: Some(version.trim().to_string()),
                    error: None,
                })
            } else {
                let error = String::from_utf8_lossy(&output.stderr);
                Ok(PythonResult {
                    success: false,
                    data: None,
                    error: Some(error.to_string()),
                })
            }
        }
        Err(e) => {
            // 尝试python3
            let output3 = Command::new("python3")
                .arg("--version")
                .output();
            
            match output3 {
                Ok(output3) => {
                    if output3.status.success() {
                        let version = String::from_utf8_lossy(&output3.stdout);
                        Ok(PythonResult {
                            success: true,
                            data: Some(format!("python3: {}", version.trim())),
                            error: None,
                        })
                    } else {
                        Ok(PythonResult {
                            success: false,
                            data: None,
                            error: Some(format!("Python环境检查失败: {}", e)),
                        })
                    }
                }
                Err(_) => {
                    Ok(PythonResult {
                        success: false,
                        data: None,
                        error: Some("未找到Python环境，请确保已安装Python".to_string()),
                    })
                }
            }
        }
    }
}

/// 启动yimiaojue Python程序
#[tauri::command]
pub async fn start_yimiaojue(app_handle: AppHandle) -> Result<PythonResult, String> {
    println!("🚀 启动yimiaojue程序...");

    let yimiaojue_path = get_yimiaojue_path(&app_handle)?;
    let main_script = yimiaojue_path.join("ocr查询.py");

    // 详细打印路径信息
    let absolute_yimiaojue = std::fs::canonicalize(&yimiaojue_path)
        .unwrap_or_else(|_| yimiaojue_path.clone());
    let absolute_script = std::fs::canonicalize(&main_script)
        .unwrap_or_else(|_| main_script.clone());

    println!("📂 yimiaojue目录: {:?}", absolute_yimiaojue);
    println!("📄 主脚本文件: {:?}", absolute_script);

    if !main_script.exists() {
        let error_msg = format!("主程序文件不存在: {:?}", absolute_script);
        println!("❌ {}", error_msg);
        return Ok(PythonResult {
            success: false,
            data: None,
            error: Some(error_msg),
        });
    }
    
    // 详细打印启动命令
    let script_str = main_script.to_str().unwrap();
    println!("🐍 执行命令: python {:?}", script_str);
    println!("📁 工作目录: {:?}", absolute_yimiaojue);

    // 先测试Python环境
    println!("🔍 测试Python环境...");
    let python_test = Command::new("python")
        .arg("--version")
        .output();

    match python_test {
        Ok(output) => {
            let version = String::from_utf8_lossy(&output.stdout);
            println!("✅ Python版本: {}", version.trim());
        }
        Err(e) => {
            println!("❌ Python环境测试失败: {}", e);
        }
    }

    // 检查文件权限和可读性
    println!("🔍 检查文件权限...");
    match std::fs::metadata(&main_script) {
        Ok(metadata) => {
            println!("✅ 文件大小: {} bytes", metadata.len());
            println!("✅ 文件权限: {:?}", metadata.permissions());
        }
        Err(e) => {
            println!("❌ 无法读取文件元数据: {}", e);
        }
    }

    // 创建临时目录并复制Python程序
    println!("📁 创建临时运行目录...");
    let temp_dir = std::env::temp_dir().join("tft_yimiaojue");
    if temp_dir.exists() {
        let _ = fs::remove_dir_all(&temp_dir);
    }
    fs::create_dir_all(&temp_dir).map_err(|e| format!("创建临时目录失败: {}", e))?;

    // 复制整个yimiaojue目录到临时目录
    println!("📋 复制Python程序到临时目录...");
    copy_dir_all(&absolute_yimiaojue, &temp_dir).map_err(|e| format!("复制文件失败: {}", e))?;

    // 获取数据库路径并传递给Python
    let db_path = crate::database::get_database_path();
    println!("📊 数据库路径: {}", db_path);

    // 尝试启动Python程序（无头模式）
    println!("🚀 启动Python程序（无头模式）...");
    let result = Command::new("python")
        .arg("ocr查询.py")
        .arg("--headless")  // 无头模式
        .arg("--api-port")
        .arg("8888")  // API端口
        .env("TFT_DB_PATH", &db_path)  // 通过环境变量传递数据库路径
        .current_dir(&temp_dir)  // 在临时目录运行
        .env("PYTHONDONTWRITEBYTECODE", "1")  // 禁用字节码生成
        .env("PYTHONIOENCODING", "utf-8")     // 设置编码
        .stdout(Stdio::piped())
        .stderr(Stdio::piped())
        .spawn();

    match result {
        Ok(mut child) => {
            println!("✅ Python进程已启动，PID: {:?}", child.id());

            // 等待一小段时间检查进程是否立即退出
            std::thread::sleep(std::time::Duration::from_millis(1000));

            match child.try_wait() {
                Ok(Some(status)) => {
                    // 进程已经退出
                    println!("❌ Python进程已退出，状态: {:?}", status);

                    // 读取输出
                    let mut stdout = String::new();
                    let mut stderr = String::new();

                    if let Some(mut stdout_handle) = child.stdout.take() {
                        use std::io::Read;
                        let _ = stdout_handle.read_to_string(&mut stdout);
                    }

                    if let Some(mut stderr_handle) = child.stderr.take() {
                        use std::io::Read;
                        let _ = stderr_handle.read_to_string(&mut stderr);
                    }

                    println!("📤 Python stdout: {}", stdout);
                    println!("📤 Python stderr: {}", stderr);

                    let error_msg = format!("❌ Python进程启动后立即退出\n状态: {:?}\nstdout: {}\nstderr: {}\n📂 路径: {:?}\n📄 脚本: {:?}",
                        status, stdout, stderr, absolute_yimiaojue, absolute_script);

                    Ok(PythonResult {
                        success: false,
                        data: None,
                        error: Some(error_msg),
                    })
                }
                Ok(None) => {
                    // 进程仍在运行
                    let success_msg = format!("✅ yimiaojue程序启动成功，进程正在运行\nPID: {:?}\n📂 路径: {:?}\n📄 脚本: {:?}",
                        child.id(), absolute_yimiaojue, absolute_script);
                    println!("{}", success_msg);

                    // 不等待进程结束，让它在后台运行
                    std::mem::forget(child);

                    Ok(PythonResult {
                        success: true,
                        data: Some(success_msg),
                        error: None,
                    })
                }
                Err(e) => {
                    let error_msg = format!("❌ 检查进程状态失败: {}\n📂 路径: {:?}\n📄 脚本: {:?}",
                        e, absolute_yimiaojue, absolute_script);
                    println!("{}", error_msg);

                    Ok(PythonResult {
                        success: false,
                        data: None,
                        error: Some(error_msg),
                    })
                }
            }
        }
        Err(e) => {
            println!("❌ python命令失败: {}", e);

            // 尝试直接执行看看错误信息
            println!("🔍 尝试获取详细错误信息...");
            let debug_result = Command::new("python")
                .arg("ocr查询.py")
                .current_dir(&absolute_yimiaojue)
                .output();

            match debug_result {
                Ok(output) => {
                    let stdout = String::from_utf8_lossy(&output.stdout);
                    let stderr = String::from_utf8_lossy(&output.stderr);
                    println!("📤 Python stdout: {}", stdout);
                    println!("📤 Python stderr: {}", stderr);
                    println!("📤 Exit code: {}", output.status);
                }
                Err(debug_e) => {
                    println!("❌ 无法获取详细错误: {}", debug_e);
                }
            }

            // 尝试使用python3
            println!("🔄 尝试python3（无头模式）...");
            let result3 = Command::new("python3")
                .arg("ocr查询.py")
                .arg("--headless")
                .arg("--api-port")
                .arg("8888")
                .env("TFT_DB_PATH", &db_path)  // 通过环境变量传递数据库路径
                .current_dir(&temp_dir)  // 在临时目录运行
                .env("PYTHONDONTWRITEBYTECODE", "1")  // 禁用字节码生成
                .env("PYTHONIOENCODING", "utf-8")     // 设置编码
                .stdout(Stdio::piped())
                .stderr(Stdio::piped())
                .spawn();

            match result3 {
                Ok(_child) => {
                    let success_msg = format!("✅ yimiaojue程序启动成功 (python3)\n📂 路径: {:?}\n📄 脚本: {:?}", absolute_yimiaojue, absolute_script);
                    println!("{}", success_msg);
                    Ok(PythonResult {
                        success: true,
                        data: Some(success_msg),
                        error: None,
                    })
                }
                Err(e3) => {
                    let error_msg = format!("❌ 启动失败:\n- python spawn error: {}\n- python3 spawn error: {}\n📂 路径: {:?}\n📄 脚本: {:?}", e, e3, absolute_yimiaojue, absolute_script);
                    println!("{}", error_msg);
                    Ok(PythonResult {
                        success: false,
                        data: None,
                        error: Some(error_msg),
                    })
                }
            }
        }
    }
}

/// 检查yimiaojue程序状态
#[tauri::command]
pub async fn check_yimiaojue_status(app_handle: AppHandle) -> Result<YimiaoJueStatus, String> {
    let yimiaojue_path = get_yimiaojue_path(&app_handle)?;
    
    // 检查必要文件是否存在
    let main_script = yimiaojue_path.join("ocr查询.py");
    let config_file = yimiaojue_path.join("config.py");
    
    if !main_script.exists() {
        return Ok(YimiaoJueStatus {
            running: false,
            message: "主程序文件不存在".to_string(),
        });
    }
    
    if !config_file.exists() {
        return Ok(YimiaoJueStatus {
            running: false,
            message: "配置文件不存在".to_string(),
        });
    }
    
    // 简单检查：如果文件都存在，认为可以运行
    Ok(YimiaoJueStatus {
        running: true,
        message: "yimiaojue程序就绪".to_string(),
    })
}

/// 获取详细的Python信息
#[tauri::command]
pub async fn get_python_details() -> Result<PythonResult, String> {
    println!("获取Python详细信息...");

    let info_script = r#"
import sys
import os
print("=== Python Environment Details ===")
print("Python executable:", sys.executable)
print("Python version:", sys.version)
print("Python path:", sys.path[0:3])
print("Current working directory:", os.getcwd())
print("Environment PATH:")
for path in os.environ.get('PATH', '').split(os.pathsep)[:5]:
    if 'python' in path.lower():
        print("  ", path)
"#;

    let output = Command::new("python")
        .arg("-c")
        .arg(info_script)
        .env("PYTHONIOENCODING", "utf-8")
        .output();

    match output {
        Ok(output) => {
            let stdout = String::from_utf8_lossy(&output.stdout);
            let stderr = String::from_utf8_lossy(&output.stderr);

            Ok(PythonResult {
                success: output.status.success(),
                data: Some(stdout.to_string()),
                error: if stderr.is_empty() { None } else { Some(stderr.to_string()) },
            })
        }
        Err(e) => {
            Ok(PythonResult {
                success: false,
                data: None,
                error: Some(format!("执行失败: {}", e)),
            })
        }
    }
}

/// 直接运行Python脚本并获取输出
#[tauri::command]
pub async fn run_python_script_direct(app_handle: AppHandle) -> Result<PythonResult, String> {
    println!("🧪 直接运行Python脚本...");

    let yimiaojue_path = get_yimiaojue_path(&app_handle)?;
    let main_script = yimiaojue_path.join("ocr查询.py");

    if !main_script.exists() {
        return Ok(PythonResult {
            success: false,
            data: None,
            error: Some(format!("主程序文件不存在: {:?}", main_script)),
        });
    }

    let script_str = main_script.to_str().unwrap();
    let absolute_yimiaojue = std::fs::canonicalize(&yimiaojue_path)
        .unwrap_or_else(|_| yimiaojue_path.clone());

    println!("🐍 直接执行: python {:?}", script_str);
    println!("📁 工作目录: {:?}", absolute_yimiaojue);

    let output = Command::new("python")
        .arg("ocr查询.py")  // 只使用文件名，因为工作目录已经设置
        .current_dir(&absolute_yimiaojue)
        .env("PYTHONIOENCODING", "utf-8")
        .output();

    match output {
        Ok(output) => {
            let stdout = String::from_utf8_lossy(&output.stdout);
            let stderr = String::from_utf8_lossy(&output.stderr);

            println!("📤 Exit code: {}", output.status);
            println!("📤 Stdout: {}", stdout);
            println!("📤 Stderr: {}", stderr);

            let result_data = format!("Exit code: {}\n\nStdout:\n{}\n\nStderr:\n{}",
                output.status, stdout, stderr);

            Ok(PythonResult {
                success: output.status.success(),
                data: Some(result_data),
                error: if stderr.is_empty() { None } else { Some(stderr.to_string()) },
            })
        }
        Err(e) => {
            Ok(PythonResult {
                success: false,
                data: None,
                error: Some(format!("执行失败: {}", e)),
            })
        }
    }
}

/// 测试Python脚本执行
#[tauri::command]
pub async fn test_python_script(app_handle: AppHandle) -> Result<PythonResult, String> {
    println!("测试Python脚本执行...");
    
    let yimiaojue_path = get_yimiaojue_path(&app_handle)?;
    
    // 创建一个简单的测试脚本（避免特殊字符编码问题）
    let test_script = r#"
# -*- coding: utf-8 -*-
import sys
import os
print("Python environment test successful")
print("Python version:", sys.version)
print("Current working directory:", os.getcwd())
print("yimiaojue module import test...")
try:
    import config
    print("[OK] config module imported successfully")
except ImportError as e:
    print("[ERROR] config module import failed:", str(e))
try:
    import utils
    print("[OK] utils module imported successfully")
except ImportError as e:
    print("[ERROR] utils module import failed:", str(e))
try:
    import ocr_handler
    print("[OK] ocr_handler module imported successfully")
except ImportError as e:
    print("[ERROR] ocr_handler module import failed:", str(e))
"#;
    
    let output = Command::new("python")
        .arg("-c")
        .arg(test_script)
        .current_dir(&yimiaojue_path)
        .env("PYTHONIOENCODING", "utf-8")  // 设置Python输出编码
        .output();
    
    match output {
        Ok(output) => {
            let stdout = String::from_utf8_lossy(&output.stdout);
            let stderr = String::from_utf8_lossy(&output.stderr);
            
            if output.status.success() {
                Ok(PythonResult {
                    success: true,
                    data: Some(stdout.to_string()),
                    error: if stderr.is_empty() { None } else { Some(stderr.to_string()) },
                })
            } else {
                Ok(PythonResult {
                    success: false,
                    data: Some(stdout.to_string()),
                    error: Some(stderr.to_string()),
                })
            }
        }
        Err(e) => {
            Ok(PythonResult {
                success: false,
                data: None,
                error: Some(format!("执行失败: {}", e)),
            })
        }
    }
}

/// 调用Python API的通用函数
async fn call_python_api(endpoint: &str, method: &str) -> Result<PythonResult, String> {
    let url = format!("http://localhost:8888/api/{}", endpoint);
    println!("🌐 调用API: {} {}", method, url);

    let client = reqwest::Client::new();
    let response = match method {
        "GET" => client.get(&url).send().await,
        "POST" => client.post(&url).send().await,
        _ => return Err("不支持的HTTP方法".to_string()),
    };

    match response {
        Ok(resp) => {
            let status = resp.status();
            let text = resp.text().await.unwrap_or_default();

            println!("📡 API响应状态: {}", status);
            println!("📡 API响应内容: {}", text);

            if status.is_success() {
                // 尝试解析JSON响应
                match serde_json::from_str::<serde_json::Value>(&text) {
                    Ok(_json) => {
                        Ok(PythonResult {
                            success: true,
                            data: Some(text),
                            error: None,
                        })
                    }
                    Err(_) => {
                        Ok(PythonResult {
                            success: true,
                            data: Some(text),
                            error: None,
                        })
                    }
                }
            } else {
                Ok(PythonResult {
                    success: false,
                    data: None,
                    error: Some(format!("API调用失败: {} - {}", status, text)),
                })
            }
        }
        Err(e) => {
            let error_msg = format!("API调用错误: {}", e);
            println!("❌ {}", error_msg);
            Ok(PythonResult {
                success: false,
                data: None,
                error: Some(error_msg),
            })
        }
    }
}

/// 获取OCR状态
#[tauri::command]
pub async fn get_ocr_status() -> Result<PythonResult, String> {
    call_python_api("status", "GET").await
}

/// 手动扫描海克斯
#[tauri::command]
pub async fn manual_scan_hex() -> Result<PythonResult, String> {
    call_python_api("scan_hex", "POST").await
}

/// 手动扫描装备
#[tauri::command]
pub async fn manual_scan_equipment() -> Result<PythonResult, String> {
    call_python_api("scan_equipment", "POST").await
}

/// 切换自动模式
#[tauri::command]
pub async fn toggle_auto_mode() -> Result<PythonResult, String> {
    call_python_api("toggle_auto", "POST").await
}

/// 递归复制目录
fn copy_dir_all(src: &std::path::Path, dst: &std::path::Path) -> std::io::Result<()> {
    fs::create_dir_all(dst)?;
    for entry in fs::read_dir(src)? {
        let entry = entry?;
        let ty = entry.file_type()?;
        let src_path = entry.path();
        let dst_path = dst.join(entry.file_name());

        if ty.is_dir() {
            copy_dir_all(&src_path, &dst_path)?;
        } else {
            fs::copy(&src_path, &dst_path)?;
        }
    }
    Ok(())
}
