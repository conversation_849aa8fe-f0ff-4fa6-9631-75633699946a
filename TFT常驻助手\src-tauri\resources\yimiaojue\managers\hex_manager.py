# -*- coding: utf-8 -*-
"""
业务逻辑管理器模块

本文件包含用于管理特定业务逻辑的"经理"类。
每个管理器类封装一个独立功能模块的所有逻辑，包括状态管理、后台循环、UI交互等。
"""
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import re
import imagehash
import sqlite3
import logging # 引入日志模块
from PIL import Image, ImageEnhance
from concurrent.futures import ThreadPoolExecutor, as_completed

import config
import utils
import ocr_handler
import data_query
from dynamic_ocr_timer import DynamicOCRTimer

class HexManager:
    """
    海克斯识别功能的总管理器。
    封装了与海克斯识别相关的所有状态、UI元素和业务逻辑。
    """
    def __init__(self, app_ref):
        """
        初始化HexManager。

        Args:
            app_ref (TFTAssistantApp): 主应用TFTAssistantApp的实例引用。
                                       用于访问主窗口、屏幕尺寸等共享资源。
        """
        self.app = app_ref  # 对主应用的引用
        self.hex_query = data_query.EnhancedHextechQuery()
        self.hex_whitelist = self._load_hex_whitelist()

        # --- 状态变量 ---
        self.hex_ocr_running = False
        self.plan_detection_active = False # [新增] 用于控制非阻塞方案检测的标志
        self.hex_start_time = 0
        self.hex_no_detect_time = 0
        self.current_hex_plan = 1  # 1-方案一，2-方案二
        
        # --- 动态时序控制器 ---
        self.ocr_timer = DynamicOCRTimer(
            initial_timeout=5,         # 自动触发5秒超时
            manual_timeout=10,         # 手动触发10秒超时
            extension_timeout=5,       # 有效结果延长5秒
            window_disappear_delay=5   # 窗口消失后5秒延迟关闭
        )

        # --- [新增] 缓存的游戏窗口坐标和监测优化 ---
        self.cached_game_window_rect = None
        self.last_window_check_time = 0
        self.window_check_interval = 10.0  # 每10秒重新检测一次窗口坐标

        # --- 新的状态管理机制 ---
        self.last_hex_image_hashes = [None, None, None]  # 用于检测图像变化的哈希值
        self.pending_results = {}  # 存储待处理的OCR结果
        self.stable_results = [None, None, None]    # 用于存储稳定的识别结果

        # --- 图像与哈希相关 ---
        self.hash_threshold = 10.0 # 提高阈值以忽略游戏内微小动画，防止闪烁
        self.confidence_threshold = 70 # OCR识别成功的分数阈值, 从80下调
        self.last_hex_image_hashes_lock = threading.Lock()

        # --- UI元素 ---
        self.hex_overlay_windows = []
        self.hex_rank_labels = []
        self.hex_variant_widgets = [None, None, None] # 用于显示海克斯变体的窗口
        self.hex_query_widgets = [None, None, None] 
        self.hex_debug_boxes = [] # [新增] 用于显示OCR区域的调试红框
        self.hex_extra_info_window_height = int(self.app.hex_extra_info_font_size * 2.2) # 调整高度计算
        self._create_hex_widgets() # 初始化时创建UI组件
        self._create_debug_boxes() # [新增] 初始化时创建调试红框

        # --- 结果跟踪 ---
        self.last_hex_names = [None, None, None]
        self.last_hex_ranks = [None, None, None]
        self.first_result_logged = False
        
    def _load_hex_whitelist(self):
        """从数据库加载海克斯白名单。"""
        try:
            conn = sqlite3.connect(config.HEX_DB_PATH)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM hexes")
            whitelist = [row[0] for row in cursor.fetchall()]
            conn.close()
            return whitelist
        except sqlite3.Error as e:
            messagebox.showerror("数据库错误", f"无法从数据库加载海克斯名称列表：{e}")
            return []

    def _create_hex_widgets(self):
        """创建海克斯功能所需的所有顶层窗口和标签。"""
        screen_width = self.app.screen_width
        screen_height = self.app.screen_height

        # 方案一的覆盖区域
        HEX_OVERLAY_REGIONS_RELATIVE_PLAN1 = [
            (664/2560, 1020/1440, 820/2560, 1080/1440),
            (1206/2560, 1020/1440, 1362/2560, 1080/1440),
            (1748/2560, 1020/1440, 1904/2560, 1080/1440)
        ]
        # 方案二的覆盖区域
        HEX_OVERLAY_REGIONS_RELATIVE_PLAN2 = [
            (588/2560, 965/1440, 744/2560, 1015/1440),
            (1270/2560, 965/1440, 1426/2560, 1015/1440),
            (1815/2560, 965/1440, 1971/2560, 1015/1440)
        ]
        
        self.HEX_OVERLAY_PLANS = {
            1: HEX_OVERLAY_REGIONS_RELATIVE_PLAN1,
            2: HEX_OVERLAY_REGIONS_RELATIVE_PLAN2
        }
        
        # 先创建好所有UI组件，初始状态为隐藏
        for i in range(3):
            # 主评级窗口
            overlay_window = tk.Toplevel(self.app)
            overlay_window.overrideredirect(True)
            overlay_window.attributes('-alpha', 0.8)
            overlay_window.attributes('-topmost', True)
            rank_label = ttk.Label(overlay_window, text="", style="HexRank.TLabel", anchor="center")
            rank_label.pack(expand=True, fill=tk.BOTH)
            self.hex_overlay_windows.append(overlay_window)
            self.hex_rank_labels.append(rank_label)
            overlay_window.withdraw()
            
        self.update_hex_windows_positions() # 根据默认方案设置初始位置

    def _create_debug_boxes(self):
        """[新增] 创建用于调试的红色OCR区域指示框。"""
        # 使用一种不太可能出现的颜色作为透明色
        TRANSPARENT_COLOR = '#abcdef'

        for i in range(3):
            debug_box = tk.Toplevel(self.app)
            debug_box.overrideredirect(True)
            debug_box.attributes('-topmost', True)
            # 设置透明色
            debug_box.attributes('-transparentcolor', TRANSPARENT_COLOR)
            # 设置窗口背景为透明色
            debug_box.config(bg=TRANSPARENT_COLOR)
            # 禁用窗口，让鼠标事件穿透
            debug_box.attributes('-disabled', 1)

            # 创建一个带红色边框的Frame来实现红框效果
            frame = tk.Frame(debug_box, 
                             highlightbackground="red", 
                             highlightcolor="red", 
                             highlightthickness=2, 
                             bg=TRANSPARENT_COLOR)
            frame.pack(expand=True, fill=tk.BOTH)

            self.hex_debug_boxes.append(debug_box)
            debug_box.withdraw() # 初始状态为隐藏

    def start_hex_ocr(self, event=None, manual=True):
        """
        启动海克斯OCR。
        Args:
            manual (bool): True表示由用户手动点击触发，False表示由后台自动触发。
        """
        if self.is_running():
            if manual:
                # 确保弹窗显示在最顶层
                if hasattr(self.app, 'hidden_mode') and self.app.hidden_mode:
                    temp_window = tk.Toplevel()
                    temp_window.withdraw()
                    temp_window.attributes('-topmost', True)
                    messagebox.showinfo("提示", "海克斯扫描已在运行中。", parent=temp_window)
                    temp_window.destroy()
                else:
                    messagebox.showinfo("提示", "海克斯扫描已在运行中。", parent=self.app)
            return # 如果已经在运行，则不重复启动

        logging.info(f"启动海克斯OCR功能 (触发方式: {'手动' if manual else '自动'})。")
        if manual:
            # 确保弹窗显示在最顶层
            if hasattr(self.app, 'hidden_mode') and self.app.hidden_mode:
                # 无头模式下创建临时顶层窗口来显示消息
                temp_window = tk.Toplevel()
                temp_window.withdraw()  # 隐藏窗口本身
                temp_window.attributes('-topmost', True)
                messagebox.showinfo("海克斯OCR", "海克斯评分已开启", parent=temp_window)
                temp_window.destroy()
            else:
                messagebox.showinfo("海克斯OCR", "海克斯评分已开启", parent=self.app)

        self._get_game_window_rect_cached(force_update=True)

        self.hex_ocr_running = True
        self.plan_detection_active = True
        self.hex_start_time = time.time()
        self.first_result_logged = False # 重置计时标志
        
        # 启动动态时序控制器
        self.ocr_timer.start_timer(manual=manual)
        
        self.current_hex_plan = 1
        config.SCREENSHOT_REGIONS_RELATIVE = config.SCREENSHOT_REGIONS_RELATIVE_PLAN1
        self.update_hex_windows_positions()

        if self.app.debug_mode:
            self._show_debug_boxes()

        threading.Thread(target=self.run_hex_ocr_loop, daemon=True).start()

    def stop_hex_ocr(self):
        """停止海克斯OCR循环并清理资源。"""
        self.hex_ocr_running = False
        self.plan_detection_active = False # [修改] 停止时重置标志
        
        # 停止动态时序控制器
        self.ocr_timer.stop_timer()
        
        logging.info("停止海克斯OCR功能。") # 日志记录

        # [新增] 停止时，总是隐藏调试红框
        self._hide_debug_boxes()

        # 在主线程中安全地隐藏所有窗口
        self.app.after(0, self.hide_all_windows)
        
        # [v6 修正] 重置所有与单次OCR任务相关的状态，确保下次启动是全新的
        self.last_hex_names = [None, None, None]
        self.last_hex_ranks = [None, None, None]
        self.last_hex_image_hashes = [None, None, None]
        self.pending_results.clear()
        self.stable_results = [None, None, None]
        
        # 确保所有变体窗口也被关闭
        self.app.after(0, self._hide_all_variant_windows)

        if self.app.debug_mode:
            print("海克斯OCR已停止并清理资源。")
            logging.debug("海克斯OCR已停止并清理资源。")

    def _check_and_switch_plan(self):
        """[新增] 检测是否存在刷新按钮，并根据结果自动切换海克斯识别方案。"""
        try:
            # 获取游戏窗口坐标
            game_window_rect = self._get_game_window_rect_cached()
            if not game_window_rect or game_window_rect[2] == 0 or game_window_rect[3] == 0:
                # 窗口无效或未找到，不执行检测
                return

            # 计算刷新按钮区域的绝对坐标
            x1, y1, x2, y2 = utils.convert_relative_to_absolute(config.REFRESH_BUTTON_REGION_RELATIVE, game_window_rect)
            
            # 截图
            img = ocr_handler.capture_screen((x1, y1, x2, y2))
            if not img:
                # 截图失败
                return

            # [修改] 计算图像的两种哈希值 (p-hash 和 d-hash)
            current_phash = imagehash.phash(img)
            current_dhash = imagehash.dhash(img)
            
            # 从配置加载参考哈希值
            stored_phash = imagehash.hex_to_hash(config.REFRESH_BUTTON_PHASH)
            stored_dhash = imagehash.hex_to_hash(config.REFRESH_BUTTON_DHASH)

            # 计算哈希差异
            phash_diff = current_phash - stored_phash
            dhash_diff = current_dhash - stored_dhash
            
            # [修改] 根据任一哈希差异判断当前应使用的方案 (2=有刷新按钮, 1=常规)
            # 如果任一哈希差异小于阈值，说明图像相似，即检测到了刷新按钮
            is_detected = (phash_diff <= config.REFRESH_BUTTON_HASH_THRESHOLD or 
                           dhash_diff <= config.REFRESH_BUTTON_HASH_THRESHOLD)
            detected_plan = 2 if is_detected else 1
            
            # 如果检测到的方案与当前方案不同，则执行切换
            if detected_plan != self.current_hex_plan:
                logging.info(f"[Hex] 检测到界面变化，自动切换方案: {self.current_hex_plan} -> {detected_plan} (p-hash差异: {phash_diff}, d-hash差异: {dhash_diff})")
                
                # 更新当前方案
                self.current_hex_plan = detected_plan
                
                # 根据新方案更新截图区域配置
                if self.current_hex_plan == 1:
                    config.SCREENSHOT_REGIONS_RELATIVE = config.SCREENSHOT_REGIONS_RELATIVE_PLAN1
                else:
                    config.SCREENSHOT_REGIONS_RELATIVE = config.SCREENSHOT_REGIONS_RELATIVE_PLAN2
                
                # 在主UI线程中更新所有窗口的位置
                self.app.after(0, self.update_hex_windows_positions)
                
                # 如果在调试模式下，确保调试红框也更新
                if self.app.debug_mode:
                    self.app.after(0, self._show_debug_boxes)

        except Exception as e:
            logging.error(f"[Hex] 自动检测海克斯方案时出错: {e}", exc_info=True)

    def run_hex_ocr_loop(self):
        """
        海克斯OCR的主循环。
        使用动态时序控制器管理运行时间。
        """
        plan_locked_and_logged = False

        while self.hex_ocr_running:
            try:
                # --- 快速响应停止信号 ---
                if not self.hex_ocr_running:
                    logging.info("[Hex] 检测到停止信号，立即退出循环")
                    break
                
                # --- 动态超时检测 ---
                if not self.ocr_timer.should_continue():
                    logging.info("[Hex] 动态时序控制器指示停止，执行清理")
                    self.stop_hex_ocr()
                    break
                
                loop_start_time = time.time()
                current_time = time.time()

                # --- 方案检测 (保留原有逻辑) ---
                plan_check_duration = 0
                if self.plan_detection_active:
                    plan_check_start = time.time()
                    if current_time - self.hex_start_time < 10:
                        self._check_and_switch_plan()
                    else:
                        self.plan_detection_active = False
                    plan_check_duration = time.time() - plan_check_start
                
                if not self.plan_detection_active and not plan_locked_and_logged:
                    logging.info(f"[Hex] 方案检测结束，已锁定为: 方案{self.current_hex_plan}")
                    plan_locked_and_logged = True

                # --- 截图与OCR核心逻辑 (保留原有逻辑) ---
                screenshot_hash_start = time.time()
                game_window_rect = self._get_game_window_rect_cached()
                images_to_process = {}
                
                current_regions = (config.SCREENSHOT_REGIONS_RELATIVE_PLAN2 if self.current_hex_plan == 2
                                 else config.SCREENSHOT_REGIONS_RELATIVE_PLAN1)
                
                for i, rel_region in enumerate(current_regions):
                    if not game_window_rect:
                        continue
                    
                    x1, y1, x2, y2 = utils.convert_relative_to_absolute(rel_region, game_window_rect)
                    img = ocr_handler.capture_screen((x1, y1, x2, y2))
                    if img:
                        current_hash = imagehash.phash(img)
                        
                        if (self.last_hex_image_hashes[i] is None or
                            utils.hash_distance(current_hash, self.last_hex_image_hashes[i]) > self.hash_threshold):
                            
                            images_to_process[i] = img
                            self.last_hex_image_hashes[i] = current_hash
                            
                            # [v4 修复] 当图像变化时，只清除数据，把UI决策完全交给UI控制器
                            # 这可以防止在识别出新内容前，窗口不必要地闪烁（消失）
                            if self.stable_results[i] is not None:
                                self.stable_results[i] = None
                screenshot_hash_duration = time.time() - screenshot_hash_start
                
                ocr_dispatch_duration = 0
                if images_to_process:
                    ocr_dispatch_start = time.time()
                    
                    # 步骤 1: [重构] 使用更简单的批量OCR识别，移除复杂的多策略重试
                    image_list = list(images_to_process.values())
                    index_list = list(images_to_process.keys())
                    
                    # RapidOcr引擎内部已并行，此处无需再套线程池
                    raw_text_parts, _ = ocr_handler.ocr_recognize(image_list, psm=7)
                    
                    # 步骤 2: 对每个OCR结果进行智能查询和匹配
                    for i, raw_text in enumerate(raw_text_parts):
                        index = index_list[i]
                        if not raw_text:
                            continue

                        query_result = self.hex_query.smart_query(raw_text)
                        
                        # 步骤 3: 如果匹配成功，构造标准结果对象
                        if query_result and query_result.get('status') == 'exact':
                            result_data = {
                                'match': query_result['data']['name'],
                                'candidates': [query_result['data']['name']],
                                'score': query_result['data'].get('score', 100),
                                'status': 'SUCCESS'
                            }
                            # 将结果放入待处理队列，交由UI控制器更新
                            self.pending_results[index] = {'status': 'SUCCESS', 'data': result_data}
                            
                            # 通知动态时序控制器检测到有效结果
                            self.ocr_timer.extend_timer()
                            
                    ocr_dispatch_duration = time.time() - ocr_dispatch_start

                self.app.after(0, self._update_ui_controller)
                
                consistency_check_start = time.time()
                self._check_window_consistency()
                consistency_check_duration = time.time() - consistency_check_start
                
                time.sleep(0.05)  # 减少sleep时间，提高停止响应速度

                # 打印性能日志
                loop_duration = time.time() - loop_start_time
                # [优化] 仅在有识别任务时才打印详细性能日志
                if ocr_dispatch_duration > 0:
                    perf_log = (f"[Hex Perf] Total: {loop_duration:.3f}s | "
                                f"Plan Check: {plan_check_duration:.3f}s | "
                                f"Screenshot/Hash: {screenshot_hash_duration:.3f}s | "
                                f"Consistency: {consistency_check_duration:.3f}s | "
                                f"OCR Dispatch: {ocr_dispatch_duration:.3f}s")
                    logging.info(perf_log)

            except Exception as e:
                logging.error(f"Hex OCR 主循环异常: {e}", exc_info=True)
                time.sleep(1)
        
        # 循环退出后确保清理资源
        if self.hex_ocr_running:
            logging.info("[Hex] OCR循环退出，执行最终清理")
            self.stop_hex_ocr()

    def _update_ui_controller(self):
        """全局UI渲染控制器，根据当前状态决定显示内容。"""
        # 1. 将待处理结果更新到稳定结果中
        for index, result_data in list(self.pending_results.items()):
            if result_data:
                self.stable_results[index] = result_data.get('data')
                del self.pending_results[index]

        # 2. 核心逻辑：根据稳定结果的数量决定UI行为
        successful_recognitions = sum(1 for r in self.stable_results if r is not None)

        # 3. 检查窗口可见状态并通知时序控制器
        windows_visible = self._check_windows_visibility()
        self.ocr_timer.set_windows_visible(windows_visible)

        if successful_recognitions >= 2:
            # 当至少有两个识别成功时，我们认为进入了海克斯选择界面
            if not self.first_result_logged:
                total_duration = time.time() - self.hex_start_time
                logging.info(f"[Hex] 首次识别到结果并显示，总耗时: {total_duration:.2f}s")
                self.first_result_logged = True
                
            for index, result in enumerate(self.stable_results):
                if result:
                    # --- 该位置有识别结果 ---
                    # 1. 确保查询窗口是关闭的
                    self.hide_hex_query_windows(index)

                    # 2. 正常显示评级窗口 (带防闪烁)
                    new_name = result.get('match')
                    if new_name and new_name == self.last_hex_names[index] and self.hex_overlay_windows[index].winfo_ismapped():
                        continue
                    
                    self.last_hex_names[index] = new_name
                    query_result = self.hex_query.smart_query(new_name)
                    self.update_hex_rank_label(index, query_result)
                    self.hex_overlay_windows[index].deiconify()
                    self._handle_variant_display(index, new_name, result.get('candidates', []))
                else:
                    # --- 该位置无识别结果 ---
                    # 1. 确保评级和变体窗口是隐藏的
                    self.hex_overlay_windows[index].withdraw()
                    self._hide_variant_window(index)
                    
                    # 2. 清除名称缓存，以便下次能重新识别
                    self.last_hex_names[index] = None
                    
                    # 3. 如果查询窗口不存在，则创建它 (防闪烁的关键)
                    if self.hex_query_widgets[index] is None:
                        self.create_hex_query_window(index)
        else:
            # 当识别成功的数量少于2个时，隐藏所有窗口
            # [v4 修复] 修正AttributeError，正确检查字典和窗口对象
            is_any_window_visible = any(
                (w['window'].winfo_ismapped() if isinstance(w, dict) else w.winfo_ismapped())
                for w_list in [self.hex_overlay_windows, self.hex_query_widgets, self.hex_variant_widgets]
                for w in w_list if w
            )
            if is_any_window_visible:
                self.hide_all_windows()
                # 清除所有缓存，为下一次干净的识别做准备
                self.last_hex_names = [None, None, None]
                self.stable_results = [None, None, None]

    def _check_windows_visibility(self) -> bool:
        """
        检查是否有海克斯窗口当前可见
        
        Returns:
            bool: True表示有窗口可见，False表示所有窗口都不可见
        """
        try:
            # 检查主评级窗口
            for window in self.hex_overlay_windows:
                if window.winfo_exists() and window.winfo_ismapped():
                    return True
            
            # 检查变体窗口
            for variant_widget in self.hex_variant_widgets:
                if (variant_widget and 
                    variant_widget['window'].winfo_exists() and 
                    variant_widget['window'].winfo_ismapped()):
                    return True
            
            # 检查查询窗口
            for query_widget in self.hex_query_widgets:
                if (query_widget and 
                    query_widget['window'].winfo_exists() and 
                    query_widget['window'].winfo_ismapped()):
                    return True
            
            return False
            
        except Exception as e:
            if self.app.debug_mode:
                logging.debug(f"[Hex] 检查窗口可见性时出错: {e}")
            return False

    def _check_window_consistency(self):
        """[新增] 仿照参考代码，检查并确保主评级窗口和变体窗口的显示状态同步。"""
        try:
            for i in range(3):
                main_window = self.hex_overlay_windows[i]
                if not main_window.winfo_exists():
                    continue

                # 如果主窗口不可见，但其对应的变体窗口可见，则隐藏变体窗口
                variant_widget = self.hex_variant_widgets[i]
                if not main_window.winfo_ismapped() and variant_widget and variant_widget['window'].winfo_exists() and variant_widget['window'].winfo_ismapped():
                    if self.app.debug_mode:
                        print(f"[一致性检查] 主窗口 {i} 已隐藏，同步隐藏悬空的变体窗口。")
                    self._hide_variant_window(i)

        except Exception as e:
            if self.app.debug_mode:
                print(f"窗口一致性检查出错: {e}")

    def _flash_effect_start(self, index):
        """为指定索引的窗口启动闪烁效果（第一步：隐藏）"""
        # 这是一个简单的实现，通过立即隐藏旧内容来给用户反馈
        self.hide_all_windows_for_index(index)

    def update_hex_rank_label(self, label_index, result):
        """更新单个海克斯评级标签的样式和文本。"""
        label = self.hex_rank_labels[label_index]
        
        if result['status'] == 'exact':
            current_rank = result['data']['rank']
            target_style = f"HexRank{current_rank}.TLabel" if current_rank in ['S', 'A', 'B', 'C', 'D'] else "HexRank.TLabel"
            label.config(text=current_rank, style=target_style)
        else:
            label.config(text="?", style="HexRank.TLabel")

    def _handle_variant_display(self, index, hex_name, candidates):
        """检查并决定是否显示海克斯变体列表窗口。"""
        if not hex_name or not candidates:
            self._hide_variant_window(index)
            return

        variants = None
        # [修复] 检查是否有多个变体，直接对字符串列表进行迭代
        if any(re.search(r'[ⅠⅡⅢⅣⅤI]+$', c) for c in candidates):
            base_name = self.hex_query._preprocess_name(hex_name)
            variants = self.hex_query.get_variants_by_base_name(base_name)
        
        # 检查特殊情况
        elif "之徽" in hex_name or "之冕" in hex_name or "之环" in hex_name:
            variants = self.hex_query.handle_special_variants(hex_name)

        if variants and len(variants) > 1:
            # 如果找到变体，则创建或更新变体窗口
            self._create_variant_window(index, variants)
        else:
            # 如果没有变体，确保旧的变体窗口是关闭的
            self._hide_variant_window(index)

    def _create_variant_window(self, index, variants):
        """为指定位置创建一个显示海克斯变体及其评级的窗口。"""
        # [优化] 检查现有窗口和内容是否匹配，防止不必要的重绘和抖动
        existing_widget = self.hex_variant_widgets[index]
        if existing_widget and existing_widget.get('variants') == variants:
            # 内容相同，确保窗口可见即可
            if not existing_widget['window'].winfo_ismapped():
                existing_widget['window'].deiconify()
            return

        # 如果已有窗口但内容不同，或没有窗口，先安全地销毁旧的
        if self.hex_variant_widgets[index] is not None:
            self._hide_variant_window(index)
            
        # 获取主评级窗口的位置和大小作为参考
        ref_win = self.hex_overlay_windows[index]
        x, y, w, h = ref_win.winfo_x(), ref_win.winfo_y(), ref_win.winfo_width(), ref_win.winfo_height()

        variant_window = tk.Toplevel(self.app)
        variant_window.overrideredirect(True)
        variant_window.attributes('-topmost', True)
        
        main_frame = ttk.Frame(variant_window, style="Card.TFrame")
        main_frame.pack(expand=True, fill="both")
        
        title_label = ttk.Label(main_frame, text="相关海克斯评级", style="QueryTitle.TLabel")
        title_label.pack(pady=(5,2))
        
        listbox = tk.Listbox(main_frame, height=len(variants), background="#333", foreground="#CCC", selectbackground="#0078D7", relief="flat", borderwidth=0, highlightthickness=0)
        listbox.pack(expand=True, fill='both', padx=5, pady=5)
        
        # 填充列表
        for name, rank in variants.items():
            display_text = f"{name}  [{rank}]"
            listbox.insert(tk.END, display_text)
            
        # 动态计算窗口高度和位置
        # 强制UI更新以获取真实的尺寸
        variant_window.update_idletasks()
        
        # 获取计算后的实际高度
        win_height = variant_window.winfo_height()
        
        # 将窗口置于主评级窗口的上方
        new_y = y - win_height
        
        # 重新设置几何位置，这次只移动，不改变大小
        variant_window.geometry(f"{w}x{win_height}+{x}+{new_y}")

        self.hex_variant_widgets[index] = {
            'window': variant_window,
            'variants': variants # 保存用于创建窗口的数据，用于后续比较
        }

        # 点击外部或窗口本身都会关闭
        variant_window.bind("<FocusOut>", lambda e, i=index: self._hide_variant_window(i))
        listbox.bind("<Button-1>", lambda e, i=index: self._hide_variant_window(i))
        
        variant_window.deiconify()
        
    def _hide_variant_window(self, index):
        """隐藏并销毁指定索引的海克斯变体窗口，并立即清除其引用以防止竞态条件。"""
        # 立即捕获要销毁的窗口小部件并清除列表中的引用，这是修复竞态条件的关键
        widget_to_destroy = self.hex_variant_widgets[index]
        self.hex_variant_widgets[index] = None

        if widget_to_destroy:
            def _destroy():
                try:
                    widget_to_destroy['window'].destroy()
                except tk.TclError:
                    pass  # 窗口可能已被销毁

            # 在UI线程中安排销毁
            self.app.after(0, _destroy)

    def _hide_all_variant_windows(self):
        """隐藏并销毁所有的海克斯变体窗口。"""
        for i in range(len(self.hex_variant_widgets)):
            self._hide_variant_window(i)

    def create_hex_query_window(self, index):
        """为指定的海克斯位置创建一个智能的手动查询窗口。"""
        # 如果该位置已有窗口，先销毁
        if self.hex_query_widgets[index] is not None:
            self.hide_hex_query_windows(index)

        # 获取主评级窗口的位置和大小作为参考
        ref_win = self.hex_overlay_windows[index]
        x, y, w, h = ref_win.winfo_x(), ref_win.winfo_y(), ref_win.winfo_width(), ref_win.winfo_height()
        
        query_window = tk.Toplevel(self.app)
        query_window.overrideredirect(True)
        query_window.attributes('-topmost', True)

        # UI布局
        main_frame = ttk.Frame(query_window, style="Card.TFrame")
        main_frame.pack(expand=True, fill="both")
        
        title_label = ttk.Label(main_frame, text="手动搜索 (输入名称/拼音)", style="QueryTitle.TLabel")
        title_label.pack(pady=(5,2))

        entry_var = tk.StringVar()
        entry = ttk.Entry(main_frame, textvariable=entry_var)
        entry.pack(fill='x', padx=5)

        listbox = tk.Listbox(main_frame, height=5, background="#333", foreground="#CCC", selectbackground="#0078D7", relief="flat", borderwidth=0, highlightthickness=0)
        listbox.pack(expand=True, fill='both', padx=5, pady=5)
        
        # 调整窗口大小和位置
        # 高度大约是标题+输入框+5行列表的高度
        win_height = int(h * 4) 
        # 将窗口置于主评级窗口的上方
        new_y = y - win_height
        query_window.geometry(f"{w}x{win_height}+{x}+{new_y}")

        # 存储所有组件
        self.hex_query_widgets[index] = {
            'window': query_window,
            'entry': entry,
            'listbox': listbox,
            'var': entry_var
        }

        # 绑定事件
        entry.bind("<KeyRelease>", lambda e, i=index: self._on_hex_query_keyup(e, i))
        listbox.bind("<<ListboxSelect>>", lambda e, i=index: self.on_hex_query_select(e, i))
        # 增加回车确认
        listbox.bind("<Return>", lambda e, i=index: self.on_hex_query_select(e, i))
        # 增加点击外部关闭窗口的功能
        query_window.bind("<FocusOut>", lambda e, i=index: self.hide_hex_query_windows(i))

        query_window.deiconify()
        entry.focus_set()

    def _on_hex_query_keyup(self, event, index):
        """处理手动查询输入框的键盘抬起事件，实时更新建议列表。"""
        widgets = self.hex_query_widgets[index]
        if not widgets:
            return

        query = widgets['var'].get()
        # 调用模糊搜索算法
        results = self.hex_query.fuzzy_search_hex(query)
        
        listbox = widgets['listbox']
        listbox.delete(0, tk.END) # 清空列表
        
        # 为每个结果获取评级并显示在建议列表中
        for name in results:
            result_obj = self.hex_query.smart_query(name)
            rank = "?"
            # 确保查询成功并获取到评级
            if result_obj and result_obj.get('status') == 'exact':
                rank = result_obj['data']['rank']
            
            # 将海克斯名称和评级一起显示
            display_text = f"{name}  [{rank}]"
            listbox.insert(tk.END, display_text)

    def on_hex_query_select(self, event, index):
        """处理手动查询下拉框的选择事件。"""
        widgets = self.hex_query_widgets[index]
        if not widgets:
            return
        
        listbox = widgets['listbox']
        # 获取选中的项目
        selected_indices = listbox.curselection()
        if not selected_indices:
            # 如果列表没有选中项，但按了回车，尝试使用列表中的第一项
            if event.keysym == 'Return' and listbox.size() > 0:
                selected_text = listbox.get(0)
            else:
                return
        else:
            selected_text = listbox.get(selected_indices[0])
        
        # 从 "海克斯名称  [评级]" 的格式中解析出真正的海克斯名称
        selected_name = selected_text.split("  [")[0]

        if selected_name:
            # [修复] 伪造一个与OCR结果格式一致的stable_result，让UI控制器能够渲染它
            self.stable_results[index] = {
                'match': selected_name,
                'candidates': [selected_name], # 提供一个候选列表以供变体检测使用
                'score': 100, # 手动选择，可以认为是100%置信度
                'status': 'SUCCESS'
            }
            # 手动选择后，我们没有图像哈希，所以设为None以便下次能检测到图像变化
            self.last_hex_image_hashes[index] = None
            
            # 隐藏并销毁查询窗口，UI的更新会由主循环的_update_ui_controller处理
            self.hide_hex_query_windows(index)
            # 立即手动触发一次UI更新
            self.app.after(0, self._update_ui_controller)

    def hide_hex_query_windows(self, index=None):
        """隐藏并销毁指定索引或所有的海克斯查询窗口，并立即清除引用以防止竞态条件。"""
        
        # 确定要操作的索引列表
        indices_to_hide = range(len(self.hex_query_widgets)) if index is None else [index]

        for i in indices_to_hide:
            # 检查索引有效性
            if i < len(self.hex_query_widgets):
                # 立即捕获要销毁的窗口小部件并清除列表中的引用
                widget_to_destroy = self.hex_query_widgets[i]
                self.hex_query_widgets[i] = None 

                if widget_to_destroy:
                    # 使用闭包来正确捕获每次循环的widget_to_destroy值
                    def schedule_destroy(widget):
                        def _destroy_callback():
                            try:
                                widget['window'].destroy()
                            except tk.TclError:
                                pass # 窗口可能已被销毁
                        self.app.after(0, _destroy_callback)
                    
                    schedule_destroy(widget_to_destroy)

    def hide_all_windows(self):
        """隐藏所有由HexManager创建的窗口"""
        for i in range(3):
            self.hide_all_windows_for_index(i)

    def hide_all_windows_for_index(self, index):
        """隐藏指定索引的所有窗口"""
        if self.hex_overlay_windows[index]: self.hex_overlay_windows[index].withdraw()
        self.hide_hex_query_windows(index)
        self._hide_variant_window(index) # 确保相关评级窗口（变体窗口）也一并隐藏
    
    def update_hex_windows_positions(self):
        """根据当前方案更新所有海克斯窗口和调试框的位置和大小。"""
        regions = self.HEX_OVERLAY_PLANS.get(self.current_hex_plan, [])
        # [修改] 使用OCR截图区域来定位调试框
        ocr_regions = config.SCREENSHOT_REGIONS_RELATIVE_PLAN1 if self.current_hex_plan == 1 else config.SCREENSHOT_REGIONS_RELATIVE_PLAN2

        if not regions: return

        # [新增] 获取游戏窗口坐标用于位置计算
        game_window_rect = self._get_game_window_rect_cached()
        logging.info(f"[Hex] 更新窗口位置时获取游戏窗口坐标: {game_window_rect}")

        for i in range(3):
            # [修改] 更新评级窗口 - 使用新的坐标转换
            rx1, ry1, rx2, ry2 = utils.convert_relative_to_absolute(regions[i], game_window_rect)
            w, h = rx2 - rx1, ry2 - ry1
            self.hex_overlay_windows[i].geometry(f"{w}x{h}+{rx1}+{ry1}")

            # [修改] 更新调试红框 - 使用新的坐标转换
            if self.hex_debug_boxes and len(self.hex_debug_boxes) > i:
                ocr_x1, ocr_y1, ocr_x2, ocr_y2 = utils.convert_relative_to_absolute(ocr_regions[i], game_window_rect)
                ocr_w, ocr_h = ocr_x2 - ocr_x1, ocr_y2 - ocr_y1
                self.hex_debug_boxes[i].geometry(f"{ocr_w}x{ocr_h}+{ocr_x1}+{ocr_y1}")
                if self.app.debug_mode:
                    print(f"[HexManager] 调试框 {i} 位置更新: {ocr_w}x{ocr_h}+{ocr_x1}+{ocr_y1}")

    def switch_hex_plan(self):
        """切换海克斯识别方案并更新窗口位置。"""
        self.current_hex_plan = 2 if self.current_hex_plan == 1 else 1
        
        if self.current_hex_plan == 1:
            config.SCREENSHOT_REGIONS_RELATIVE = config.SCREENSHOT_REGIONS_RELATIVE_PLAN1
        else:
            config.SCREENSHOT_REGIONS_RELATIVE = config.SCREENSHOT_REGIONS_RELATIVE_PLAN2
            
        self.update_hex_windows_positions()
        plan_text = "一" if self.current_hex_plan == 1 else "二"
        messagebox.showinfo("切换成功", f"已切换到海克斯方案 {plan_text}")

    def is_running(self):
        """返回海克斯OCR是否正在运行。"""
        return self.hex_ocr_running

    def _show_debug_boxes(self):
        """[新增] 显示调试红框。"""
        for box in self.hex_debug_boxes:
            box.deiconify()

    def _hide_debug_boxes(self):
        """[新增] 隐藏调试红框。"""
        for box in self.hex_debug_boxes:
            box.withdraw()

    def destroy(self):
        """
        快速销毁HexManager资源（已由主程序处理，此方法保留兼容性）
        """
        logging.info("[Hex] 海克斯管理器资源清理完成")
        self.hex_ocr_running = False
        # 主程序已经处理了窗口销毁，这里只需要停止循环

    def _get_game_window_rect_cached(self, force_update=False):
        """
        [新增] 获取缓存的游戏窗口坐标，减少频繁的API调用。
        
        Args:
            force_update (bool): 是否强制更新缓存
            
        Returns:
            tuple: (x, y, width, height) 游戏窗口坐标
        """
        current_time = time.time()
        
        # 判断是否需要更新缓存
        if (force_update or 
            self.cached_game_window_rect is None or 
            current_time - self.last_window_check_time > self.window_check_interval):
            
            # 获取新的窗口坐标
            self.cached_game_window_rect = utils.get_game_window_rect(use_client_area=True)
            self.last_window_check_time = current_time
            
            if self.app.debug_mode:
                print(f"[HexManager] 更新游戏窗口坐标缓存: {self.cached_game_window_rect}")
            logging.info(f"[Hex] 更新游戏窗口坐标缓存: {self.cached_game_window_rect}")
        
        return self.cached_game_window_rect