use rusqlite::{Connection, Result as SqliteResult, Row};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH};
use tauri::command;
use thiserror::Error;
use tokio::sync::RwLock;

// 错误类型定义
#[derive(Error, Debug)]
pub enum DatabaseError {
    #[error("SQLite错误: {0}")]
    SqliteError(#[from] rusqlite::Error),
}

// 查询结果类型
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct QueryResult {
    pub data: Vec<serde_json::Value>,
    pub error: Option<String>,
}

// 缓存项结构
#[derive(Clone, Debug)]
struct CacheItem {
    data: QueryResult,
    timestamp: u64,
}

// 数据库管理器
pub struct DatabaseManager {
    db_path: String,
    query_cache: Arc<RwLock<HashMap<String, CacheItem>>>,
    cache_expiry_seconds: u64,
    max_cache_size: usize,
}

impl DatabaseManager {
    pub fn new(db_path: String) -> Self {
        Self {
            db_path,
            query_cache: Arc::new(RwLock::new(HashMap::new())),
            cache_expiry_seconds: 300, // 5分钟缓存过期
            max_cache_size: 100,       // 最多缓存100个查询
        }
    }

    // 获取数据库连接
    fn get_connection(&self) -> SqliteResult<Connection> {
        let conn = Connection::open(&self.db_path)?;
        conn.execute("PRAGMA foreign_keys = ON;", [])?;
        Ok(conn)
    }

    // 获取当前时间戳
    fn current_timestamp() -> u64 {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs()
    }

    // 检查并清理过期缓存
    async fn cleanup_expired_cache(&self) {
        let mut cache = self.query_cache.write().await;
        let current_time = Self::current_timestamp();

        cache.retain(|_, item| current_time - item.timestamp < self.cache_expiry_seconds);

        // 如果缓存仍然太大，删除最旧的条目
        if cache.len() > self.max_cache_size {
            let mut items: Vec<_> = cache
                .iter()
                .map(|(k, v)| (k.clone(), v.timestamp))
                .collect();
            items.sort_by_key(|(_, timestamp)| *timestamp);

            let to_remove = cache.len() - self.max_cache_size / 2;
            for (key, _) in items.iter().take(to_remove) {
                cache.remove(key);
            }
        }
    }

    // 从缓存获取查询结果
    async fn get_cached_result(&self, cache_key: &str) -> Option<QueryResult> {
        let cache = self.query_cache.read().await;
        if let Some(item) = cache.get(cache_key) {
            let current_time = Self::current_timestamp();
            if current_time - item.timestamp < self.cache_expiry_seconds {
                return Some(item.data.clone());
            }
        }
        None
    }

    // 缓存查询结果
    async fn cache_result(&self, cache_key: String, result: QueryResult) {
        self.cleanup_expired_cache().await;

        let mut cache = self.query_cache.write().await;
        cache.insert(
            cache_key,
            CacheItem {
                data: result,
                timestamp: Self::current_timestamp(),
            },
        );
    }

    // 将Row转换为JSON值
    fn row_to_json(row: &Row) -> rusqlite::Result<serde_json::Value> {
        let column_count = row.as_ref().column_count();
        let mut map = serde_json::Map::new();

        for i in 0..column_count {
            let column_name = row.as_ref().column_name(i)?;
            let value: serde_json::Value = match row.get_ref(i)? {
                rusqlite::types::ValueRef::Null => serde_json::Value::Null,
                rusqlite::types::ValueRef::Integer(i) => serde_json::Value::Number(i.into()),
                rusqlite::types::ValueRef::Real(f) => serde_json::Value::Number(
                    serde_json::Number::from_f64(f).unwrap_or(serde_json::Number::from(0)),
                ),
                rusqlite::types::ValueRef::Text(s) => {
                    serde_json::Value::String(String::from_utf8_lossy(s).to_string())
                }
                rusqlite::types::ValueRef::Blob(b) => {
                    use base64::Engine;
                    serde_json::Value::String(base64::engine::general_purpose::STANDARD.encode(b))
                }
            };
            map.insert(column_name.to_string(), value);
        }

        Ok(serde_json::Value::Object(map))
    }

    // 执行查询
    pub async fn execute_query(
        &self,
        sql: &str,
        params: Vec<String>,
        cache_key: Option<String>,
    ) -> QueryResult {
        // 检查缓存
        if let Some(ref key) = cache_key {
            if let Some(cached_result) = self.get_cached_result(key).await {
                return cached_result;
            }
        }

        // 执行查询
        let result = match self.get_connection() {
            Ok(conn) => {
                // 转换参数
                let params_refs: Vec<&dyn rusqlite::ToSql> =
                    params.iter().map(|p| p as &dyn rusqlite::ToSql).collect();

                let mut stmt = match conn.prepare(sql) {
                    Ok(stmt) => stmt,
                    Err(e) => {
                        return QueryResult {
                            data: vec![],
                            error: Some(format!("准备SQL语句失败: {}", e)),
                        };
                    }
                };

                let rows_result = stmt.query_map(&params_refs[..], Self::row_to_json);
                match rows_result {
                    Ok(rows) => {
                        let mut data = Vec::new();
                        for row_result in rows {
                            match row_result {
                                Ok(json_value) => data.push(json_value),
                                Err(e) => {
                                    return QueryResult {
                                        data: vec![],
                                        error: Some(format!("处理查询结果失败: {}", e)),
                                    };
                                }
                            }
                        }
                        QueryResult { data, error: None }
                    }
                    Err(e) => QueryResult {
                        data: vec![],
                        error: Some(format!("执行查询失败: {}", e)),
                    },
                }
            }
            Err(e) => QueryResult {
                data: vec![],
                error: Some(format!("数据库连接失败: {}", e)),
            },
        };

        // 缓存结果
        if let Some(key) = cache_key {
            if result.error.is_none() {
                self.cache_result(key, result.clone()).await;
            }
        }

        result
    }

    // 清空查询缓存
    pub async fn clear_cache(&self) {
        let mut cache = self.query_cache.write().await;
        cache.clear();
    }
}

// 全局数据库管理器实例
lazy_static::lazy_static! {
    static ref DB_MANAGER: DatabaseManager = {
        // 按优先级查找数据库文件
        let db_path = find_database_path();
        println!("🗄️ 数据库已连接: {}", db_path);
        DatabaseManager::new(db_path)
    };
}

// 公开函数：获取当前使用的数据库路径
pub fn get_database_path() -> String {
    find_database_path()
}

// 查找数据库文件路径
fn find_database_path() -> String {
    let db_filename = "tft_data.db";
    
    // 1. 首先尝试项目根目录（TFT常驻助手的上级目录）
    if let Ok(current_dir) = std::env::current_dir() {
        // 如果当前目录是src-tauri，则查找上级目录的上级目录（项目根目录）
        let root_dir = if current_dir.file_name().and_then(|n| n.to_str()) == Some("src-tauri") {
            // src-tauri -> TFT常驻助手 -> 项目根目录
            current_dir.parent()
                .and_then(|p| p.parent())
                .unwrap_or(&current_dir)
                .to_path_buf()
        } else {
            // 尝试找到项目根目录（包含TFT常驻助手文件夹的目录）
            let mut check_dir = current_dir.clone();
            loop {
                let root_db = check_dir.join(db_filename);
                if root_db.exists() {
                    return root_db.to_string_lossy().to_string();
                }
                
                if let Some(parent) = check_dir.parent() {
                    check_dir = parent.to_path_buf();
                } else {
                    break;
                }
            }
            current_dir.clone()
        };
        
        let root_db = root_dir.join(db_filename);
        if root_db.exists() {
            return root_db.to_string_lossy().to_string();
        }
        
        // 2. 尝试当前工作目录
        let current_db = current_dir.join(db_filename);
        if current_db.exists() {
            return current_db.to_string_lossy().to_string();
        }
        
        // 3. 尝试父目录
        if let Some(parent_dir) = current_dir.parent() {
            let parent_db = parent_dir.join(db_filename);
            if parent_db.exists() {
                return parent_db.to_string_lossy().to_string();
            }
        }
    }
    
    // 4. 尝试可执行文件所在目录
    if let Ok(exe_path) = std::env::current_exe() {
        if let Some(exe_dir) = exe_path.parent() {
            let exe_db = exe_dir.join(db_filename);
            if exe_db.exists() {
                return exe_db.to_string_lossy().to_string();
            }
            
            // 5. 尝试可执行文件的多级父目录
            let mut check_dir = exe_dir.to_path_buf();
            for _ in 0..3 { // 最多向上查找3级目录
                if let Some(parent) = check_dir.parent() {
                    check_dir = parent.to_path_buf();
                    let parent_db = check_dir.join(db_filename);
                    if parent_db.exists() {
                        return parent_db.to_string_lossy().to_string();
                    }
                }
            }
        }
    }
    
    // 6. 如果都没找到，使用当前目录作为默认值
    let fallback_path = std::env::current_dir()
        .unwrap_or_else(|_| std::path::PathBuf::from("."))
        .join(db_filename);
    
    println!("⚠️ 未找到数据库文件，使用默认路径: {:?}", fallback_path);
    println!("💡 请确保运行 数据库建立.py 脚本生成数据库文件");
    fallback_path.to_string_lossy().to_string()
}

// Tauri命令：执行通用查询
#[command]
pub async fn execute_query_command(
    sql: String,
    params: Vec<String>,
    cache_key: Option<String>,
) -> QueryResult {
    DB_MANAGER.execute_query(&sql, params, cache_key).await
}

// Tauri命令：清空查询缓存
#[command]
pub async fn clear_query_cache() -> Result<(), String> {
    DB_MANAGER.clear_cache().await;
    Ok(())
}

// === 核心数据查询命令 ===

// Tauri命令：获取阵容列表
#[command]
pub async fn get_comp_list() -> QueryResult {
    let sql = r#"
        SELECT 
            cb.name,
            cb.comp_id,
            cb.tier,
            cb.avg_placement,
            cb.frequency,
            cb.win_rate,
            cb.top4_rate,
            GROUP_CONCAT(DISTINCT h.cn_name) as heroes
        FROM comps_base cb
        LEFT JOIN comp_heroes ch ON cb.comp_id = ch.comp_id
        LEFT JOIN heroes h ON ch.hero_name = h.cn_name
        GROUP BY cb.comp_id, cb.name
        ORDER BY 
            CASE cb.tier 
                WHEN 'S' THEN 1 
                WHEN 'A' THEN 2 
                WHEN 'B' THEN 3 
                WHEN 'C' THEN 4 
                WHEN 'D' THEN 5 
                ELSE 6 
            END,
            cb.avg_placement ASC
    "#;

    DB_MANAGER
        .execute_query(sql, vec![], Some("comp_list".to_string()))
        .await
}

// Tauri命令：获取英雄列表
#[command]
pub async fn get_hero_list() -> QueryResult {
    let sql = r#"
        SELECT 
            h.cn_name,
            h.en_name,
            h.cost,
            h.icon_path,
            h.play_rate,
            h.avg_place,
            h.top4_rate,
            h.top1_rate
        FROM heroes h
        ORDER BY h.cost ASC, h.cn_name ASC
    "#;

    DB_MANAGER
        .execute_query(sql, vec![], Some("hero_list".to_string()))
        .await
}

// Tauri命令：获取装备列表
#[command]
pub async fn get_item_list() -> QueryResult {
    let sql = r#"
        SELECT
            i.name as cn_name,
            i.en_name,
            i.tier,
            i.icon_url,
            i.icon_path,
            i.avg_placement as avg_place,
            i.top1_rate,
            i.play_rate
        FROM items i
        ORDER BY
            CASE i.tier
                WHEN 'S' THEN 1
                WHEN 'A' THEN 2
                WHEN 'B' THEN 3
                WHEN 'C' THEN 4
                WHEN 'D' THEN 5
                ELSE 6
            END,
            i.play_rate DESC
    "#;

    DB_MANAGER
        .execute_query(sql, vec![], Some("item_list".to_string()))
        .await
}

// Tauri命令：获取海克斯列表
#[command]
pub async fn get_hex_list() -> QueryResult {
    println!("🔍 [Rust] 开始执行海克斯列表查询...");

    let sql = r#"
        SELECT
            h.name,
            h.tier,
            h.icon_url,
            h.icon_path,
            h.description,
            h.level
        FROM hexes h
        ORDER BY
            CASE h.tier
                WHEN 'S' THEN 1
                WHEN 'A' THEN 2
                WHEN 'B' THEN 3
                WHEN 'C' THEN 4
                WHEN 'D' THEN 5
                ELSE 6
            END,
            h.name ASC
    "#;

    let result = DB_MANAGER
        .execute_query(sql, vec![], Some("hex_list_fast".to_string()))
        .await;

    match &result.error {
        Some(err) => println!("❌ [Rust] 海克斯查询失败: {}", err),
        None => println!("✅ [Rust] 海克斯查询成功，返回{}条数据", result.data.len()),
    }

    result
}

// Tauri命令：获取海克斯详情
#[command]
pub async fn get_hex_detail(hex_name: String) -> QueryResult {
    let sql = r#"
        SELECT 
            h.name,
            h.tier,
            h.icon_url,
            h.icon_path,
            h.description,
            h.level
        FROM hexes h
        WHERE h.name = ?1
    "#;

    DB_MANAGER
        .execute_query(sql, vec![hex_name], Some("hex_detail".to_string()))
        .await
}

// === 详情数据查询命令 ===

// Tauri命令：获取阵容详情
#[command]
pub async fn get_comp_detail(comp_name: String) -> QueryResult {
    let sql = r#"
        SELECT 
            cb.*,
            cdo.total_games,
            cdo.avg_placement as detail_avg_placement,
            GROUP_CONCAT(DISTINCT h.cn_name || ':' || ch.hero_order ORDER BY ch.hero_order) as hero_configs
        FROM comps_base cb
        LEFT JOIN comps_detail_overall cdo ON cb.name = cdo.comp_name
        LEFT JOIN comp_heroes ch ON cb.name = ch.comp_name
        LEFT JOIN heroes h ON ch.hero_cn_name = h.cn_name
        WHERE cb.name = ?
        GROUP BY cb.name
    "#;

    DB_MANAGER
        .execute_query(
            sql,
            vec![comp_name.clone()],
            Some(format!("comp_detail_{}", comp_name)),
        )
        .await
}

// Tauri命令：获取英雄详情
#[command]
pub async fn get_hero_detail(hero_name: String) -> QueryResult {
    let sql = r#"
        SELECT 
            h.*,
            GROUP_CONCAT(DISTINCT his.item_names || ':' || his.item_count || ':' || his.play_rate || ':' || his.avg_place || ':' || his.top4_rate || ':' || his.top1_rate) as item_stats
        FROM heroes h
        LEFT JOIN hero_item_stats his ON h.cn_name = his.hero_cn_name
        WHERE h.cn_name = ?
        GROUP BY h.cn_name
    "#;

    DB_MANAGER
        .execute_query(
            sql,
            vec![hero_name.clone()],
            Some(format!("hero_detail_{}", hero_name)),
        )
        .await
}

// Tauri命令：获取装备详情
#[command]
pub async fn get_item_detail(item_name: String) -> QueryResult {
    let sql = r#"
        SELECT 
            i.*,
            GROUP_CONCAT(DISTINCT ehs.hero_cn_name || ':' || ehs.play_rate || ':' || ehs.avg_place || ':' || ehs.top4_rate || ':' || ehs.top1_rate) as hero_stats,
            GROUP_CONCAT(DISTINCT icr.category_name) as categories
        FROM items i
        LEFT JOIN equipment_hero_stats ehs ON i.name = ehs.item_name
        LEFT JOIN item_category_relations icr ON i.name = icr.item_name
        WHERE i.name = ?
        GROUP BY i.name
    "#;

    DB_MANAGER
        .execute_query(
            sql,
            vec![item_name.clone()],
            Some(format!("item_detail_{}", item_name)),
        )
        .await
}

// === 羁绊相关查询命令 ===

// Tauri命令：获取羁绊列表
#[command]
pub async fn get_trait_list() -> QueryResult {
    let sql = r#"
        SELECT 
            t.name,
            t.en_name,
            t.center_icon_url,
            t.base_icon_url,
            t.center_icon_path,
            t.base_icon_path
        FROM traits t
        ORDER BY t.name ASC
    "#;

    DB_MANAGER
        .execute_query(sql, vec![], Some("trait_list".to_string()))
        .await
}

// Tauri命令：获取羁绊等级信息
#[command]
pub async fn get_trait_levels(trait_name: String) -> QueryResult {
    let sql = r#"
        SELECT 
            tl.trait_name,
            tl.level,
            tl.level_name,
            tl.level_number
        FROM trait_levels tl
        WHERE tl.trait_name = ?
        ORDER BY tl.level ASC
    "#;

    DB_MANAGER
        .execute_query(
            sql,
            vec![trait_name.clone()],
            Some(format!("trait_levels_{}", trait_name)),
        )
        .await
}

// === 阵容相关详细查询命令 ===

// Tauri命令：获取阵容单位统计
#[command]
pub async fn get_comp_unit_stats(comp_name: String) -> QueryResult {
    let sql = r#"
        SELECT 
            cdus.comp_name,
            cdus.unit_id,
            cdus.appearance_rate,
            cdus.avg_placement,
            cdus.total_count,
            h.cn_name,
            h.cost,
            h.icon_path
        FROM comp_detail_unit_stats cdus
        LEFT JOIN heroes h ON cdus.unit_id = h.en_name
        WHERE cdus.comp_name = ?
        ORDER BY cdus.appearance_rate DESC
    "#;

    DB_MANAGER
        .execute_query(
            sql,
            vec![comp_name.clone()],
            Some(format!("comp_unit_stats_{}", comp_name)),
        )
        .await
}

// Tauri命令：获取阵容羁绊统计
#[command]
pub async fn get_comp_trait_stats(comp_name: String) -> QueryResult {
    let sql = r#"
        SELECT 
            cdts.comp_name,
            cdts.trait_id,
            cdts.appearance_rate,
            cdts.avg_placement,
            cdts.total_count,
            cdts.tier,
            t.name as trait_name,
            t.center_icon_path
        FROM comp_detail_trait_stats cdts
        LEFT JOIN traits t ON cdts.trait_id = t.en_name
        WHERE cdts.comp_name = ?
        ORDER BY cdts.appearance_rate DESC
    "#;

    DB_MANAGER
        .execute_query(
            sql,
            vec![comp_name.clone()],
            Some(format!("comp_trait_stats_{}", comp_name)),
        )
        .await
}

// Tauri命令：获取阵容装备整体统计
#[command]
pub async fn get_comp_item_overall_stats(comp_name: String) -> QueryResult {
    let sql = r#"
        SELECT 
            cdios.comp_name,
            cdios.item_id,
            cdios.overall_appearance_rate,
            cdios.overall_avg_placement,
            cdios.overall_count,
            i.name as item_name,
            i.icon_path,
            i.tier
        FROM comp_detail_item_overall_stats cdios
        LEFT JOIN items i ON cdios.item_id = i.en_name
        WHERE cdios.comp_name = ?
        ORDER BY cdios.overall_appearance_rate DESC
    "#;

    DB_MANAGER
        .execute_query(
            sql,
            vec![comp_name.clone()],
            Some(format!("comp_item_overall_stats_{}", comp_name)),
        )
        .await
}

// Tauri命令：获取阵容装备单位统计
#[command]
pub async fn get_comp_item_unit_stats(comp_name: String) -> QueryResult {
    let sql = r#"
        SELECT 
            cdius.comp_name,
            cdius.item_id,
            cdius.unit_id,
            cdius.avg_placement_on_unit,
            cdius.count_on_unit,
            cdius.unit_pick_rate,
            cdius.item_pick_rate,
            i.name as item_name,
            i.icon_path,
            h.cn_name as hero_name,
            h.icon_path as hero_icon_path
        FROM comp_detail_item_unit_stats cdius
        LEFT JOIN items i ON cdius.item_id = i.en_name
        LEFT JOIN heroes h ON cdius.unit_id = h.en_name
        WHERE cdius.comp_name = ?
        ORDER BY cdius.count_on_unit DESC
    "#;

    DB_MANAGER
        .execute_query(
            sql,
            vec![comp_name.clone()],
            Some(format!("comp_item_unit_stats_{}", comp_name)),
        )
        .await
}

// Tauri命令：获取阵容分等级推荐
#[command]
pub async fn get_comp_level_recommendations(comp_name: String, level: i32) -> QueryResult {
    let sql = r#"
        SELECT 
            cdlr.comp_name,
            cdlr.level,
            cdlr.recommendation_index,
            cdlr.units,
            cdlr.traits,
            cdlr.avg_placement,
            cdlr.count
        FROM comp_detail_level_recs cdlr
        WHERE cdlr.comp_name = ? AND cdlr.level = ?
        ORDER BY cdlr.recommendation_index ASC
    "#;

    DB_MANAGER
        .execute_query(
            sql,
            vec![comp_name.clone(), level.to_string()],
            Some(format!("comp_level_recs_{}_{}", comp_name, level)),
        )
        .await
}

// === 英雄相关详细查询命令 ===

// Tauri命令：获取英雄装备统计
#[command]
pub async fn get_hero_item_stats(hero_name: String) -> QueryResult {
    let sql = r#"
        SELECT 
            his.hero_cn_name,
            his.item_count,
            his.item_names,
            his.play_rate,
            his.avg_place,
            his.top4_rate,
            his.top1_rate
        FROM hero_item_stats his
        WHERE his.hero_cn_name = ?
        ORDER BY his.item_count ASC, his.play_rate DESC
    "#;

    DB_MANAGER
        .execute_query(
            sql,
            vec![hero_name.clone()],
            Some(format!("hero_item_stats_{}", hero_name)),
        )
        .await
}

// Tauri命令：获取装备图标路径
#[command]
pub async fn get_item_icon_path(item_name: String) -> QueryResult {
    let sql = r#"
        SELECT name, icon_path
        FROM items
        WHERE name = ?
    "#;

    DB_MANAGER
        .execute_query(
            sql,
            vec![item_name.clone()],
            Some(format!("item_icon_{}", item_name)),
        )
        .await
}

// === 装备相关详细查询命令 ===

// Tauri命令：获取装备在英雄上的统计
#[command]
pub async fn get_equipment_hero_stats(item_name: String) -> QueryResult {
    let sql = r#"
        SELECT 
            ehs.item_name,
            ehs.hero_cn_name,
            ehs.play_rate,
            ehs.avg_place,
            ehs.top4_rate,
            ehs.top1_rate,
            h.cost,
            h.icon_path as hero_icon_path
        FROM equipment_hero_stats ehs
        LEFT JOIN heroes h ON ehs.hero_cn_name = h.cn_name
        WHERE ehs.item_name = ?
        ORDER BY ehs.play_rate DESC
    "#;

    DB_MANAGER
        .execute_query(
            sql,
            vec![item_name.clone()],
            Some(format!("equipment_hero_stats_{}", item_name)),
        )
        .await
}

// Tauri命令：获取装备分类
#[command]
pub async fn get_item_categories() -> QueryResult {
    let sql = r#"
        SELECT 
            ic.category_name
        FROM item_categories ic
        ORDER BY ic.category_name ASC
    "#;

    DB_MANAGER
        .execute_query(sql, vec![], Some("item_categories".to_string()))
        .await
}

// Tauri命令：获取分类下的装备
#[command]
pub async fn get_items_by_category(category_name: String) -> QueryResult {
    let sql = r#"
        SELECT 
            i.name,
            i.en_name,
            i.tier,
            i.icon_url,
            i.icon_path,
            i.avg_placement,
            i.top1_rate,
            i.play_rate
        FROM items i
        INNER JOIN item_category_relations icr ON i.name = icr.item_name
        WHERE icr.category_name = ?
        ORDER BY i.tier ASC, i.play_rate DESC
    "#;

    DB_MANAGER
        .execute_query(
            sql,
            vec![category_name.clone()],
            Some(format!("items_by_category_{}", category_name)),
        )
        .await
}

// === 数据库连接测试命令 ===

// Tauri命令：测试数据库连接
#[command]
pub async fn test_database_connection() -> Result<String, String> {
    match DB_MANAGER.get_connection() {
        Ok(conn) => {
            // 测试查询表是否存在
            let table_check_sql = "SELECT name FROM sqlite_master WHERE type='table' AND name='heroes'";
            match conn.prepare(table_check_sql) {
                Ok(mut stmt) => {
                    let table_exists = stmt.exists([]).unwrap_or(false);
                    if !table_exists {
                        return Err("heroes表不存在，请检查数据库是否正确初始化".to_string());
                    }
                    
                    // 统计英雄数量
                    let count_sql = "SELECT COUNT(*) FROM heroes";
                    match conn.prepare(count_sql) {
                        Ok(mut count_stmt) => {
                            let count: i64 = count_stmt.query_row([], |row| row.get(0)).unwrap_or(0);
                            Ok(format!("数据库连接正常，数据库路径: {}，英雄表包含 {} 条记录", DB_MANAGER.db_path, count))
                        }
                        Err(e) => Err(format!("计数查询失败: {}", e))
                    }
                }
                Err(e) => Err(format!("表检查失败: {}", e))
            }
        }
        Err(e) => Err(format!("数据库连接失败: {}，数据库路径: {}", e, DB_MANAGER.db_path))
    }
}

// === 文件系统相关命令 ===

// Tauri命令：检查图标文件是否存在
#[command]
pub async fn check_icon_exists(icon_path: String) -> Result<bool, String> {
    // 构建完整的图标路径
    let full_path = if icon_path.starts_with('/') || icon_path.contains(':') {
        // 绝对路径
        std::path::PathBuf::from(icon_path)
    } else {
        // 相对路径，基于项目根目录
        let current_dir = std::env::current_dir()
            .map_err(|e| format!("获取当前目录失败: {}", e))?;
        
        // 如果当前目录是src-tauri，需要向上两级到达项目根目录
        let base_dir = if current_dir.file_name().and_then(|n| n.to_str()) == Some("src-tauri") {
            current_dir.parent()
                .and_then(|p| p.parent())
                .unwrap_or(&current_dir)
                .to_path_buf()
        } else {
            current_dir
        };
        
        // 处理Windows路径分隔符
        let normalized_path = icon_path.replace('\\', "/");
        base_dir.join(normalized_path)
    };
    
    let exists = full_path.exists();
    if !exists {
        println!("⚠️ 图标文件不存在: {:?}", full_path);
    }
    Ok(exists)
}

// Tauri命令：获取图标文件的base64数据
#[command]
pub async fn get_icon_base64(icon_path: String) -> Result<String, String> {
    use std::fs;
    use base64::Engine;
    
    // 构建完整的图标路径
    let full_path = if icon_path.starts_with('/') || icon_path.contains(':') {
        std::path::PathBuf::from(icon_path)
    } else {
        let current_dir = std::env::current_dir()
            .map_err(|e| format!("获取当前目录失败: {}", e))?;
        
        let base_dir = if current_dir.file_name().and_then(|n| n.to_str()) == Some("src-tauri") {
            current_dir.parent()
                .and_then(|p| p.parent())
                .unwrap_or(&current_dir)
                .to_path_buf()
        } else {
            current_dir
        };
        
        let normalized_path = icon_path.replace('\\', "/");
        base_dir.join(normalized_path)
    };
    
    if !full_path.exists() {
        return Err(format!("图标文件不存在: {:?}", full_path));
    }
    
    // 读取文件内容
    let file_data = fs::read(&full_path)
        .map_err(|e| format!("读取图标文件失败: {}", e))?;
    
    // 获取文件扩展名以确定MIME类型
    let mime_type = match full_path.extension().and_then(|ext| ext.to_str()) {
        Some("png") => "image/png",
        Some("jpg") | Some("jpeg") => "image/jpeg",
        Some("webp") => "image/webp",
        Some("svg") => "image/svg+xml",
        _ => "image/png", // 默认
    };
    
    // 转换为base64
    let base64_data = base64::engine::general_purpose::STANDARD.encode(&file_data);
    let data_url = format!("data:{};base64,{}", mime_type, base64_data);
    Ok(data_url)
}

// === 通用统计查询命令 ===

// Tauri命令：获取数据库统计信息
#[command]
pub async fn get_database_stats() -> QueryResult {
    let sql = r#"
        SELECT 
            'comps_base' as table_name,
            COUNT(*) as count
        FROM comps_base
        UNION ALL
        SELECT 
            'heroes' as table_name,
            COUNT(*) as count
        FROM heroes
        UNION ALL
        SELECT 
            'items' as table_name,
            COUNT(*) as count
        FROM items
        UNION ALL
        SELECT 
            'hexes' as table_name,
            COUNT(*) as count
        FROM hexes
        UNION ALL
        SELECT 
            'traits' as table_name,
            COUNT(*) as count
        FROM traits
    "#;

    DB_MANAGER
        .execute_query(sql, vec![], Some("database_stats".to_string()))
        .await
}
